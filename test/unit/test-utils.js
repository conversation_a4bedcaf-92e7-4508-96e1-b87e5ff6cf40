/**
 * Test Utilities for Unit Tests
 * 
 * This module provides common utilities, mocks, and helpers for unit testing
 * the src directory functions.
 */

/**
 * Create a mock Forge API response
 * @param {Object} data - The response data
 * @param {boolean} ok - Whether the response is successful
 * @param {number} status - HTTP status code
 * @returns {Object} Mock response object
 */
export function createMockForgeResponse(data, ok = true, status = 200) {
  return {
    ok,
    status,
    statusText: ok ? 'OK' : 'Error',
    json: async () => data,
    text: async () => JSON.stringify(data)
  };
}

/**
 * Create a mock OpenRouter API response
 * @param {string} content - The AI response content
 * @param {boolean} ok - Whether the response is successful
 * @param {number} status - HTTP status code
 * @returns {Object} Mock response object
 */
export function createMockOpenRouterResponse(content, ok = true, status = 200) {
  const data = ok ? {
    choices: [{
      message: {
        content: content
      }
    }]
  } : { error: 'API Error' };

  return {
    ok,
    status,
    statusText: ok ? 'OK' : 'Error',
    json: async () => data,
    text: async () => JSON.stringify(data)
  };
}

/**
 * Create mock Jira field data
 * @param {string} fieldKey - The field key
 * @param {string} fieldId - The field ID
 * @returns {Object} Mock field object
 */
export function createMockJiraField(fieldKey, fieldId = `customfield_${Math.floor(Math.random() * 10000)}`) {
  const fieldNameMap = {
    'simple-requirements-field': 'Simple Requirements',
    'full-requirements-field': 'Full Requirements'
  };

  return {
    id: fieldId,
    name: fieldNameMap[fieldKey] || fieldKey,
    schema: {
      type: 'string',
      custom: `com.atlassian.jira.plugin.system.customfieldtypes:${fieldKey}`
    }
  };
}

/**
 * Create mock changelog data
 * @param {string} fieldId - The field ID that was changed
 * @param {string} fieldName - The field name that was changed
 * @param {string} fromValue - The old value
 * @param {string} toValue - The new value
 * @returns {Object} Mock changelog object
 */
export function createMockChangelog(fieldId, fieldName, fromValue = '', toValue = 'new value') {
  return {
    items: [{
      fieldId,
      field: fieldName,
      from: fromValue,
      to: toValue
    }]
  };
}

/**
 * Create mock event data for Jira issue update
 * @param {string} issueId - The issue ID
 * @param {string} issueKey - The issue key
 * @param {Object} changelog - The changelog object
 * @returns {Object} Mock event object
 */
export function createMockJiraEvent(issueId = 'TEST-123', issueKey = 'TEST-123', changelog = null) {
  return {
    issue: {
      id: issueId,
      key: issueKey
    },
    changelog: changelog || createMockChangelog('customfield_10001', 'Simple Requirements')
  };
}

/**
 * Create mock context for Forge functions
 * @returns {Object} Mock context object
 */
export function createMockContext() {
  return {
    cloudId: 'test-cloud-id',
    moduleKey: 'test-module'
  };
}

/**
 * Mock console methods for testing
 * @returns {Object} Object with mock console methods and captured logs
 */
export function createMockConsole() {
  const logs = {
    log: [],
    error: [],
    warn: [],
    info: []
  };

  const mockConsole = {
    log: (...args) => logs.log.push(args.join(' ')),
    error: (...args) => logs.error.push(args.join(' ')),
    warn: (...args) => logs.warn.push(args.join(' ')),
    info: (...args) => logs.info.push(args.join(' '))
  };

  return { mockConsole, logs };
}

/**
 * Set up environment variables for testing
 * @param {Object} envVars - Environment variables to set
 */
export function setupTestEnvironment(envVars = {}) {
  const originalEnv = { ...process.env };
  
  // Set default test environment variables
  const defaultEnv = {
    SECRET_OPENROUTER_API_KEY: 'test-api-key'
  };

  Object.assign(process.env, defaultEnv, envVars);

  // Return cleanup function
  return () => {
    process.env = originalEnv;
  };
}

/**
 * Create a promise that resolves after a specified delay
 * @param {number} ms - Delay in milliseconds
 * @returns {Promise} Promise that resolves after delay
 */
export function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Assert that a function throws an error
 * @param {Function} fn - Function to test
 * @param {string|RegExp} expectedError - Expected error message or pattern
 */
export async function assertThrows(fn, expectedError) {
  let threw = false;
  let error;

  try {
    await fn();
  } catch (e) {
    threw = true;
    error = e;
  }

  if (!threw) {
    throw new Error('Expected function to throw an error');
  }

  if (typeof expectedError === 'string' && !error.message.includes(expectedError)) {
    throw new Error(`Expected error message to contain "${expectedError}", got "${error.message}"`);
  }

  if (expectedError instanceof RegExp && !expectedError.test(error.message)) {
    throw new Error(`Expected error message to match ${expectedError}, got "${error.message}"`);
  }
}

/**
 * Deep equality check for objects
 * @param {*} actual - Actual value
 * @param {*} expected - Expected value
 * @param {string} message - Error message
 */
export function assertEqual(actual, expected, message = '') {
  if (JSON.stringify(actual) !== JSON.stringify(expected)) {
    throw new Error(`${message}\nExpected: ${JSON.stringify(expected)}\nActual: ${JSON.stringify(actual)}`);
  }
}

/**
 * Check if value is truthy
 * @param {*} value - Value to check
 * @param {string} message - Error message
 */
export function assertTrue(value, message = 'Expected value to be truthy') {
  if (!value) {
    throw new Error(message);
  }
}

/**
 * Check if value is falsy
 * @param {*} value - Value to check
 * @param {string} message - Error message
 */
export function assertFalse(value, message = 'Expected value to be falsy') {
  if (value) {
    throw new Error(message);
  }
}
